import express, { Router } from 'express';
import { outletController } from '../controllers/outlet.controller';
import { authMiddleware } from '../middleware/auth';
import {
  requireAdmin,
  requireAnyRole,
  applyOutletFilter
} from '../middleware/authorization';
import {
  validateRequest,
  validateQuery,
  validateParams
} from '../middleware/validation';
import { auditMiddleware, createAuditMiddleware } from '../middleware/audit-logging';
import { AUDIT_ACTIONS, RESOURCE_TYPES } from '../../data/models/audit-log.model';
import {
  createOutletSchema,
  updateOutletSchema,
  outletIdParamSchema,
  outletQuerySchema,
  outletSearchSchema,
  bulkOutletActionSchema,
  outletStatusSchema,
} from '../../business/validators/outlet.validator';

const router: express.Router = Router();

// Apply authentication to all outlet routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /outlets
 * @desc Get all outlets with pagination and filtering
 * @access Staff, Admin (Users see only their outlet via filter)
 */
router.get(
  '/',
  requireAnyRole,
  applyOutletFilter(),
  validateQuery(outletQuerySchema),
  outletController.getAll
);

/**
 * @route GET /outlets/active
 * @desc Get only active outlets (simplified endpoint)
 * @access Staff, Admin (Users see only their outlet via filter)
 */
router.get(
  '/active',
  requireAnyRole,
  applyOutletFilter(),
  outletController.getActive
);

/**
 * @route GET /outlets/search
 * @desc Search outlets by name, address, or description
 * @access Staff, Admin (Users see only their outlet via filter)
 */
router.get(
  '/search',
  requireAnyRole,
  applyOutletFilter(),
  validateQuery(outletSearchSchema),
  outletController.search
);

/**
 * @route GET /outlets/:id
 * @desc Get outlet by ID
 * @access Staff, Admin (Users can only see their outlet)
 */
router.get(
  '/:id',
  requireAnyRole,
  validateParams(outletIdParamSchema),
  outletController.getById
);

/**
 * @route POST /outlets
 * @desc Create new outlet
 * @access Admin only
 */
router.post(
  '/',
  requireAdmin,
  validateRequest(createOutletSchema),
  outletController.create,
  auditMiddleware.outletCreated
);

/**
 * @route PUT /outlets/:id
 * @desc Update outlet
 * @access Admin only
 */
router.put(
  '/:id',
  requireAdmin,
  validateParams(outletIdParamSchema),
  validateRequest(updateOutletSchema),
  outletController.update,
  auditMiddleware.outletUpdated
);

/**
 * @route PATCH /outlets/:id/status
 * @desc Update outlet status (active/inactive)
 * @access Admin only
 */
router.patch(
  '/:id/status',
  requireAdmin,
  validateParams(outletIdParamSchema),
  validateRequest(outletStatusSchema),
  outletController.updateStatus
);

/**
 * @route DELETE /outlets/:id
 * @desc Soft delete outlet
 * @access Admin only
 */
router.delete(
  '/:id',
  requireAdmin,
  validateParams(outletIdParamSchema),
  outletController.delete,
  auditMiddleware.outletDeleted
);

/**
 * @route POST /outlets/:id/restore
 * @desc Restore soft-deleted outlet
 * @access Admin only
 */
router.post(
  '/:id/restore',
  requireAdmin,
  validateParams(outletIdParamSchema),
  outletController.restore,
  createAuditMiddleware({
    action: 'outlet.restored',
    resourceType: RESOURCE_TYPES.outlet,
    getResourceId: (req) => req.params['id'],
    getNewValues: () => ({ restoredAt: new Date().toISOString() }),
  })
);

/**
 * @route POST /outlets/bulk
 * @desc Bulk operations on outlets (activate, deactivate, delete)
 * @access Admin only
 */
router.post(
  '/bulk',
  requireAdmin,
  validateRequest(bulkOutletActionSchema),
  outletController.bulkAction,
  createAuditMiddleware({
    action: 'outlet.bulk_operation',
    resourceType: RESOURCE_TYPES.outlet,
    getResourceId: (req) => req.body.outletIds?.join(','),
    getNewValues: (req) => ({
      operation: req.body.action,
      outletIds: req.body.outletIds,
      affectedCount: req.body.outletIds?.length || 0,
    }),
  })
);

export default router;
