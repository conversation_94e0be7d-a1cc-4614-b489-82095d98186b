import React, { useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import {
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Building2,
  MapPin,
  Phone,
  Mail,
  User,
  Activity,
} from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Input } from '@/shared/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { cn } from '@/lib/utils';
import { OutletWithManager, OutletFilters } from '../types';

interface OutletTableProps {
  outlets: OutletWithManager[];
  loading?: boolean;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
  onView?: (outlet: OutletWithManager) => void;
  onBulkDelete?: (outlets: OutletWithManager[]) => void;
  onFiltersChange?: (filters: OutletFilters) => void;
  filters?: OutletFilters;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    totalPages: number;
    totalItems: number;
  };
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void;
  className?: string;
}

const columnHelper = createColumnHelper<OutletWithManager>();

export function OutletTable({
  outlets,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onBulkDelete,
  onFiltersChange,
  filters = {},
  pagination,
  onPaginationChange,
  className,
}: OutletTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [searchValue, setSearchValue] = useState(filters.search || '');

  // Handle search input changes
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onFiltersChange?.({ ...filters, search: value });
  };

  // Handle status filter changes
  const handleStatusFilterChange = (value: string) => {
    const isActive = value === 'active' ? true : value === 'inactive' ? false : undefined;
    onFiltersChange?.({ ...filters, isActive });
  };

  // Get selected outlets
  const selectedOutlets = useMemo(() => {
    return outlets.filter((_, index) => rowSelection[index]);
  }, [outlets, rowSelection]);

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedOutlets.length > 0 && onBulkDelete) {
      onBulkDelete(selectedOutlets);
      setRowSelection({});
    }
  };

  const columns = useMemo(
    () => [
      // Selection column
      columnHelper.display({
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all outlets"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={value => row.toggleSelected(!!value)}
            aria-label={`Select outlet ${row.original.name}`}
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      }),

      // Outlet name and details
      columnHelper.accessor('name', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Outlet Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const outlet = row.original;
          return (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <span className="font-medium">{outlet.name}</span>
              </div>
              {outlet.address && (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <MapPin className="h-3 w-3" />
                  <span className="truncate">{outlet.address}</span>
                </div>
              )}
            </div>
          );
        },
        size: 250,
      }),

      // Contact information
      columnHelper.display({
        id: 'contact',
        header: 'Contact',
        cell: ({ row }) => {
          const outlet = row.original;
          return (
            <div className="space-y-1">
              {outlet.phone && (
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-3 w-3 text-gray-500" />
                  <span>{outlet.phone}</span>
                </div>
              )}
              {outlet.email && (
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-3 w-3 text-gray-500" />
                  <span className="truncate">{outlet.email}</span>
                </div>
              )}
            </div>
          );
        },
        size: 200,
      }),

      // Manager
      columnHelper.accessor('manager', {
        header: 'Manager',
        cell: ({ row }) => {
          const manager = row.original.manager;
          return manager ? (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {manager.firstName} {manager.lastName}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-500">No manager assigned</span>
          );
        },
        size: 150,
      }),

      // Status
      columnHelper.accessor('isActive', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const isActive = row.original.isActive;
          return (
            <Badge variant={isActive ? 'default' : 'secondary'} className="gap-1">
              <Activity className="h-3 w-3" />
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
          );
        },
        size: 100,
      }),

      // Created date
      columnHelper.accessor('createdAt', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Created
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const date = new Date(row.original.createdAt);
          return <span className="text-sm text-gray-600">{date.toLocaleDateString()}</span>;
        },
        size: 100,
      }),

      // Actions
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => {
          const outlet = row.original;
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onView && (
                  <DropdownMenuItem onClick={() => onView(outlet)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(outlet)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Outlet
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem onClick={() => onDelete(outlet)} className="text-red-600">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Outlet
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        enableSorting: false,
        size: 80,
      }),
    ],
    [onEdit, onDelete, onView]
  );

  const table = useReactTable({
    data: outlets,
    columns,
    state: {
      sorting,
      columnFilters,
      rowSelection,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: !!pagination,
    pageCount: pagination?.totalPages || -1,
  });

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Outlets</CardTitle>
          {selectedOutlets.length > 0 && onBulkDelete && (
            <Button variant="destructive" size="sm" onClick={handleBulkDelete} className="gap-2">
              <Trash2 className="h-4 w-4" />
              Delete Selected ({selectedOutlets.length})
            </Button>
          )}
        </div>

        {/* Filters */}
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search outlets..."
              value={searchValue}
              onChange={e => handleSearchChange(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <Select
            value={
              filters.isActive === true ? 'active' : filters.isActive === false ? 'inactive' : 'all'
            }
            onValueChange={handleStatusFilterChange}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id} style={{ width: header.getSize() }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className="hover:bg-muted/50"
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {loading ? 'Loading outlets...' : 'No outlets found.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination && onPaginationChange && (
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-muted-foreground text-sm">
              Showing {pagination.pageIndex * pagination.pageSize + 1} to{' '}
              {Math.min((pagination.pageIndex + 1) * pagination.pageSize, pagination.totalItems)} of{' '}
              {pagination.totalItems} outlets
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex - 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="flex items-center gap-1">
                <span className="text-sm">
                  Page {pagination.pageIndex + 1} of {pagination.totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex + 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex >= pagination.totalPages - 1}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
