import { useState, useCallback } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { toast } from '@/shared/components/ui/use-toast';
import { useOutlets, useOutletStatsSummary, useBulkDeleteOutlets } from '../hooks/useOutlets';
import { OutletTable } from '../components/OutletTable';
import { OutletStatsCards } from '../components/OutletStatsCards';
import {
  OutletLoadingState,
  OutletErrorState,
  OutletEmptyState,
  RefreshingOutletWrapper,
} from '../components/OutletLoadingStates';
import { OutletFilters, OutletWithManager } from '../types';

export function OutletsPage() {
  // State for filters and pagination
  const [filters, setFilters] = useState<OutletFilters>({});
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // API hooks
  const {
    data: outletsResponse,
    isLoading: outletsLoading,
    error: outletsError,
    refetch: refetchOutlets,
    isFetching: outletsRefetching,
  } = useOutlets({
    ...filters,
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
  });

  const {
    data: statsData,
    isLoading: statsLoading,
    refetch: refetchStats,
  } = useOutletStatsSummary();

  const bulkDeleteMutation = useBulkDeleteOutlets();

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: OutletFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset to first page
  }, []);

  // Handle pagination changes
  const handlePaginationChange = useCallback(
    (newPagination: { pageIndex: number; pageSize: number }) => {
      setPagination(newPagination);
    },
    [],
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetchOutlets();
    refetchStats();
  }, [refetchOutlets, refetchStats]);

  // Handle outlet actions
  const handleEditOutlet = useCallback((outlet: OutletWithManager) => {
    // TODO: Implement edit outlet functionality
    toast.info('Edit Outlet', {
      description: `Edit functionality for "${outlet.name}" will be implemented.`,
    });
  }, []);

  const handleDeleteOutlet = useCallback((outlet: OutletWithManager) => {
    // TODO: Implement single outlet delete functionality
    toast.info('Delete Outlet', {
      description: `Delete functionality for "${outlet.name}" will be implemented.`,
    });
  }, []);

  const handleViewOutlet = useCallback((outlet: OutletWithManager) => {
    // TODO: Implement view outlet details functionality
    toast.info('View Outlet', {
      description: `View details for "${outlet.name}" will be implemented.`,
    });
  }, []);

  const handleBulkDelete = useCallback(
    (outlets: OutletWithManager[]) => {
      const outletIds = outlets.map(outlet => outlet.id.toString());
      bulkDeleteMutation.mutate(outletIds, {
        onSuccess: () => {
          refetchOutlets();
          refetchStats();
        },
      });
    },
    [bulkDeleteMutation, refetchOutlets, refetchStats],
  );

  const handleCreateOutlet = useCallback(() => {
    // TODO: Implement create outlet functionality
    toast.info('Create Outlet', {
      description: 'Create outlet functionality will be implemented.',
    });
  }, []);
 
  // Log API data and errors for debugging
  if (outletsResponse) {
    console.log('Outlets Response:', outletsResponse);
  }
  if (outletsError) {
    console.error('Outlets Error:', outletsError);
  }

  // Show main loading state on initial load
  if (outletsLoading && !outletsResponse) {
    return <OutletLoadingState message="Loading outlets..." />;
  }

  // Show error state if there's an error and no cached data
  if (outletsError && !outletsResponse) {
    return (
      <OutletErrorState
        error={outletsError}
        onRetry={handleRefresh}
        className="min-h-[600px]"
      />
    );
  }

  // Ensure outletsResponse is defined before destructuring
  if (!outletsResponse) {
    return (
      <OutletErrorState
        error={new Error('Failed to load outlets data.')}
        onRetry={handleRefresh}
        className="min-h-[600px]"
      />
    );
  }

  // Extract data from response
  const {
    outlets = [],
    total: totalItems = 0,
    totalPages = 0,
  } = outletsResponse;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Outlet Management</h1>
          <p className="text-muted-foreground">
            Manage outlets and their information. Total: {totalItems} outlets
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={outletsRefetching}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${outletsRefetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleCreateOutlet} className="gap-2">
            <Plus className="w-4 h-4" />
            Add Outlet
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <OutletStatsCards stats={statsData} loading={statsLoading} />

      {/* Main Content */}
      {outlets.length === 0 && !outletsLoading ? (
        <OutletEmptyState
          title="No Outlets Found"
          message={
            Object.keys(filters).length > 0
              ? 'No outlets match your current filters. Try adjusting your search criteria.'
              : 'No outlets have been created yet. Create your first outlet to get started.'
          }
          onCreateOutlet={handleCreateOutlet}
        />
      ) : (
        <RefreshingOutletWrapper isRefreshing={outletsRefetching}>
          <OutletTable
            outlets={outlets}
            loading={outletsLoading}
            onEdit={handleEditOutlet}
            onDelete={handleDeleteOutlet}
            onView={handleViewOutlet}
            onBulkDelete={handleBulkDelete}
            onFiltersChange={handleFiltersChange}
            filters={filters}
            pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
              totalPages,
              totalItems,
            }}
            onPaginationChange={handlePaginationChange}
          />
        </RefreshingOutletWrapper>
      )}
    </div>
  );
}
